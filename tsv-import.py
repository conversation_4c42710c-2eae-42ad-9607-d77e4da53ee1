#!/usr/bin/env python3
import argparse
import csv
import os
import re
import sqlite3
import sys

# Raise the CSV field size limit to handle very large fields
csv.field_size_limit(sys.maxsize)

def sanitize_column(name: str) -> str:
    """
    Turn any header name into a safe SQLite identifier:
    - strip whitespace
    - replace non-alphanumeric characters with underscores
    - prefix with underscore if it starts with a digit
    """
    name = name.strip()
    name = re.sub(r'\W+', '_', name)
    if re.match(r'^\d', name):
        name = '_' + name
    return name or '_'

def load_tsv_to_sqlite(tsv_path: str, sqlite_path: str, table_name: str):
    conn = sqlite3.connect(sqlite_path)
    cur = conn.cursor()

    with open(tsv_path, 'r', encoding='utf-8', newline='') as tsv_file:
        reader = csv.reader(tsv_file, delimiter='\t')
        try:
            raw_headers = next(reader)
        except StopIteration:
            print(f"Error: '{tsv_path}' is empty.", file=sys.stderr)
            sys.exit(1)

        columns = [sanitize_column(h) for h in raw_headers]
        col_defs = ', '.join(f'"{col}" TEXT' for col in columns)
        cur.execute(f'CREATE TABLE IF NOT EXISTS "{table_name}" ({col_defs});')

        placeholders = ', '.join('?' for _ in columns)
        col_list = ', '.join(f'"{col}"' for col in columns)
        insert_sql = f'INSERT INTO "{table_name}" ({col_list}) VALUES ({placeholders});'

        for row in reader:
            if len(row) < len(columns):
                row += [''] * (len(columns) - len(row))
            elif len(row) > len(columns):
                row = row[:len(columns)]
            cur.execute(insert_sql, row)

    conn.commit()
    conn.close()
    print(f"Loaded {tsv_path} → table '{table_name}' in {sqlite_path}")

def main():
    parser = argparse.ArgumentParser(
        description="Load a TSV file into a new SQLite DB (all TEXT columns)."
    )
    parser.add_argument('tsv_file', help="Path to your input .tsv file")
    parser.add_argument('sqlite_db', help="Path to output .sqlite (created if needed)")
    parser.add_argument(
        '-t', '--table',
        help="Name of the table to create (default: basename of TSV without extension)"
    )
    args = parser.parse_args()

    if not os.path.isfile(args.tsv_file):
        print(f"Error: '{args.tsv_file}' not found.", file=sys.stderr)
        sys.exit(1)

    default_table = os.path.splitext(os.path.basename(args.tsv_file))[0]
    table_name = args.table or default_table

    load_tsv_to_sqlite(args.tsv_file, args.sqlite_db, table_name)

if __name__ == '__main__':
    main()

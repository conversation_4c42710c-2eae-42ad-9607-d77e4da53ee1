import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Chip,
  Alert,
} from '@mui/material';
import { invoke } from '@tauri-apps/api/core';
import { useAppSelector, useAppDispatch } from '@/store';
import { getClipboardHistory } from '@/store/slices/clipboardSlice';

const ClipboardStatus: React.FC = () => {
  const dispatch = useAppDispatch();
  const { history, isMonitoring } = useAppSelector((state) => state.clipboard);
  const [status, setStatus] = useState<string>('');
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  const refreshStatus = async () => {
    try {
      const statusInfo = await invoke<string>('get_clipboard_status');
      setStatus(statusInfo);
      setLastUpdate(new Date());
      
      // Also refresh the history
      await dispatch(getClipboardHistory()).unwrap();
    } catch (error) {
      console.error('Failed to get clipboard status:', error);
      setStatus('Error getting status');
    }
  };

  const refreshHistory = async () => {
    try {
      await dispatch(getClipboardHistory()).unwrap();
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Failed to refresh history:', error);
    }
  };

  useEffect(() => {
    refreshStatus();
    
    // Auto-refresh every 5 seconds
    const interval = setInterval(refreshStatus, 5000);
    return () => clearInterval(interval);
  }, []);

  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Clipboard Monitoring Status
      </Typography>
      
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Box display="flex" alignItems="center" gap={2} mb={2}>
            <Typography variant="h6">Status:</Typography>
            <Chip 
              label={isMonitoring ? 'Active' : 'Inactive'} 
              color={isMonitoring ? 'success' : 'error'} 
            />
          </Box>
          
          <Typography variant="body2" color="text.secondary" mb={2}>
            Backend Status: {status || 'Loading...'}
          </Typography>
          
          <Typography variant="body2" color="text.secondary" mb={2}>
            History Entries: {history.length}
          </Typography>
          
          <Typography variant="body2" color="text.secondary" mb={2}>
            Last Update: {lastUpdate.toLocaleTimeString()}
          </Typography>
          
          <Box display="flex" gap={2}>
            <Button variant="outlined" onClick={refreshStatus}>
              Refresh Status
            </Button>
            <Button variant="outlined" onClick={refreshHistory}>
              Refresh History
            </Button>
          </Box>
        </CardContent>
      </Card>

      <Alert severity="info" sx={{ mb: 2 }}>
        <Typography variant="body2">
          <strong>Testing Global Clipboard Monitoring:</strong><br />
          1. Copy text from any external application (browser, text editor, etc.)<br />
          2. The app should automatically detect and add it to the history<br />
          3. Check the History tab to see new entries<br />
          4. Use the test script: <code>python3 test_clipboard.py</code>
        </Typography>
      </Alert>

      {history.length > 0 && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Recent Clipboard Entries
            </Typography>
            {history.slice(0, 5).map((entry, index) => (
              <Box key={entry.id} mb={1} p={1} bgcolor="background.default" borderRadius={1}>
                <Typography variant="body2" color="text.secondary">
                  {index + 1}. {new Date(entry.timestamp).toLocaleTimeString()}
                </Typography>
                <Typography variant="body2" sx={{ 
                  fontFamily: 'monospace', 
                  fontSize: '0.8rem',
                  wordBreak: 'break-word',
                  maxHeight: '60px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis'
                }}>
                  {entry.content.length > 100 
                    ? entry.content.substring(0, 100) + '...' 
                    : entry.content
                  }
                </Typography>
              </Box>
            ))}
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default ClipboardStatus;

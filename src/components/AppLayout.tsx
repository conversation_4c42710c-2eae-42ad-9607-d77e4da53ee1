import React, { useState } from 'react';
import {
  <PERSON>,
  Toolbar,
  <PERSON>pography,
  IconButton,
  Tooltip,
  TextField,
  Tabs,
  Tab,
  Paper,
  InputAdornment,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  PlayArrow,
  Stop,
  Search,
} from '@mui/icons-material';
import { useAppSelector, useAppDispatch } from '@/store';
import { startMonitoring, stopMonitoring, setSearchQuery } from '@/store/slices/clipboardSlice';
import ClipboardHistoryList from './ClipboardHistoryList';
import Settings from './Settings';

interface AppLayoutProps {
  children?: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const dispatch = useAppDispatch();
  const [currentView, setCurrentView] = useState<'history' | 'favorites' | 'settings'>('history');

  const { history, favorites, isMonitoring, searchQuery } = useAppSelector((state) => state.clipboard);

  const handleToggleMonitoring = () => {
    if (isMonitoring) {
      dispatch(stopMonitoring());
    } else {
      dispatch(startMonitoring());
    }
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSearchQuery(event.target.value));
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: 'history' | 'favorites') => {
    setCurrentView(newValue);
  };

  // Header component with new layout
  const Header = () => (
    <Paper
      elevation={1}
      sx={{
        borderRadius: 0,
        borderBottom: 1,
        borderColor: 'divider',
        position: 'sticky',
        top: 0,
        zIndex: 1,
      }}
    >
      <Toolbar sx={{ gap: 2 }}>
        {/* Monitoring Toggle */}
        <Tooltip title={isMonitoring ? 'Stop clipboard monitoring' : 'Start clipboard monitoring'}>
          <IconButton
            onClick={handleToggleMonitoring}
            color={isMonitoring ? 'error' : 'success'}
            size="small"
          >
            {isMonitoring ? <Stop /> : <PlayArrow />}
          </IconButton>
        </Tooltip>

        {/* Navigation Tabs */}
        <Tabs
          value={currentView === 'settings' ? false : currentView}
          onChange={handleTabChange}
          sx={{ minHeight: 'auto' }}
        >
          <Tab
            label={`History (${history.length})`}
            value="history"
            sx={{ minHeight: 'auto', py: 1 }}
          />
          <Tab
            label={`Favorites (${favorites.length})`}
            value="favorites"
            sx={{ minHeight: 'auto', py: 1 }}
          />
        </Tabs>

        {/* Smart Search Field */}
        <TextField
          placeholder="Search clipboard... (use #tag for tag filtering)"
          value={searchQuery}
          onChange={handleSearchChange}
          size="small"
          sx={{ flexGrow: 1, maxWidth: 400 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search fontSize="small" />
              </InputAdornment>
            ),
          }}
        />

        {/* Settings Button */}
        <Tooltip title="Settings">
          <IconButton
            onClick={() => setCurrentView('settings')}
            color={currentView === 'settings' ? 'primary' : 'default'}
            size="small"
          >
            <SettingsIcon />
          </IconButton>
        </Tooltip>
      </Toolbar>
    </Paper>
  );

  const renderMainContent = () => {
    switch (currentView) {
      case 'history':
        return <ClipboardHistoryList showFavoritesOnly={false} />;
      case 'favorites':
        return <ClipboardHistoryList showFavoritesOnly={true} />;
      case 'settings':
        return <Settings />;
      default:
        return children;
    }
  };

  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${DRAWER_WIDTH}px)` },
          ml: { sm: `${DRAWER_WIDTH}px` },
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {currentView === 'history' && 'Clipboard History'}
            {currentView === 'favorites' && 'Favorites'}
            {currentView === 'settings' && 'Settings'}
          </Typography>
          
          <Box display="flex" alignItems="center" gap={1}>
            <Badge
              color={isMonitoring ? 'success' : 'error'}
              variant="dot"
              sx={{
                '& .MuiBadge-badge': {
                  right: -3,
                  top: 3,
                },
              }}
            >
              <Typography variant="body2" color="inherit">
                {isMonitoring ? 'Monitoring' : 'Paused'}
              </Typography>
            </Badge>
          </Box>
        </Toolbar>
      </AppBar>
      
      <Box
        component="nav"
        sx={{ width: { sm: DRAWER_WIDTH }, flexShrink: { sm: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: DRAWER_WIDTH },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: DRAWER_WIDTH },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>
      
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { sm: `calc(100% - ${DRAWER_WIDTH}px)` },
          height: '100vh',
          overflow: 'hidden',
        }}
      >
        <Toolbar />
        <Box height="calc(100vh - 64px)" overflow="hidden">
          {renderMainContent()}
        </Box>
      </Box>
    </Box>
  );
};

export default AppLayout;

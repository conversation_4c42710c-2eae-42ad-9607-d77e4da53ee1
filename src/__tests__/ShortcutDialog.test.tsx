import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import ShortcutDialog from '@/components/ShortcutDialog';

describe('ShortcutDialog', () => {
  const mockOnClose = vi.fn();
  const mockOnSave = vi.fn();

  const defaultProps = {
    open: true,
    onClose: mockOnClose,
    onSave: mockOnSave,
    title: 'Test Shortcut',
    currentShortcut: 'CommandOrControl+Shift+V',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders dialog when open', () => {
    render(<ShortcutDialog {...defaultProps} />);
    
    expect(screen.getByText('Set Test Shortcut')).toBeInTheDocument();
    expect(screen.getByText('Current shortcut:')).toBeInTheDocument();
    expect(screen.getByText('⌘/Ctrl + ⇧ + V')).toBeInTheDocument();
  });

  it('does not render dialog when closed', () => {
    render(<ShortcutDialog {...defaultProps} open={false} />);
    
    expect(screen.queryByText('Set Test Shortcut')).not.toBeInTheDocument();
  });

  it('shows record button initially', () => {
    render(<ShortcutDialog {...defaultProps} />);
    
    expect(screen.getByText('Click to Record Shortcut')).toBeInTheDocument();
  });

  it('starts listening when record button is clicked', async () => {
    const user = userEvent.setup();
    render(<ShortcutDialog {...defaultProps} />);
    
    const recordButton = screen.getByText('Click to Record Shortcut');
    await user.click(recordButton);
    
    expect(screen.getByText('Press your key combination...')).toBeInTheDocument();
    expect(screen.getByText('Include at least one modifier (Ctrl, Alt, Shift, or Cmd)')).toBeInTheDocument();
  });

  it('detects key combination with modifiers', async () => {
    render(<ShortcutDialog {...defaultProps} />);
    
    // Start listening
    const recordButton = screen.getByText('Click to Record Shortcut');
    fireEvent.click(recordButton);
    
    // Simulate key press with modifiers
    fireEvent.keyDown(document, {
      key: 'F1',
      ctrlKey: true,
      shiftKey: true,
      preventDefault: vi.fn(),
      stopPropagation: vi.fn(),
    });
    
    await waitFor(() => {
      expect(screen.getByText('Detected shortcut:')).toBeInTheDocument();
      expect(screen.getByText('⌘/Ctrl + ⇧ + F1')).toBeInTheDocument();
    });
  });

  it('shows error for key combination without modifiers', async () => {
    render(<ShortcutDialog {...defaultProps} />);
    
    // Start listening
    const recordButton = screen.getByText('Click to Record Shortcut');
    fireEvent.click(recordButton);
    
    // Simulate key press without modifiers
    fireEvent.keyDown(document, {
      key: 'a',
      ctrlKey: false,
      shiftKey: false,
      altKey: false,
      metaKey: false,
      preventDefault: vi.fn(),
      stopPropagation: vi.fn(),
    });
    
    await waitFor(() => {
      expect(screen.getByText('Shortcut must include at least one modifier key (Ctrl, Alt, Shift, or Cmd)')).toBeInTheDocument();
    });
  });

  it('ignores modifier keys by themselves', async () => {
    render(<ShortcutDialog {...defaultProps} />);
    
    // Start listening
    const recordButton = screen.getByText('Click to Record Shortcut');
    fireEvent.click(recordButton);
    
    // Simulate pressing just a modifier key
    fireEvent.keyDown(document, {
      key: 'Control',
      ctrlKey: true,
      preventDefault: vi.fn(),
      stopPropagation: vi.fn(),
    });
    
    // Should still be listening
    expect(screen.getByText('Press your key combination...')).toBeInTheDocument();
  });

  it('maps special keys correctly', async () => {
    render(<ShortcutDialog {...defaultProps} />);
    
    // Start listening
    const recordButton = screen.getByText('Click to Record Shortcut');
    fireEvent.click(recordButton);
    
    // Test space key mapping
    fireEvent.keyDown(document, {
      key: ' ',
      ctrlKey: true,
      preventDefault: vi.fn(),
      stopPropagation: vi.fn(),
    });
    
    await waitFor(() => {
      expect(screen.getByText('⌘/Ctrl + Space')).toBeInTheDocument();
    });
  });

  it('calls onSave with correct shortcut format when save button is clicked', async () => {
    const user = userEvent.setup();
    render(<ShortcutDialog {...defaultProps} />);
    
    // Start listening and detect a key combination
    const recordButton = screen.getByText('Click to Record Shortcut');
    fireEvent.click(recordButton);
    
    fireEvent.keyDown(document, {
      key: 'F2',
      ctrlKey: true,
      altKey: true,
      preventDefault: vi.fn(),
      stopPropagation: vi.fn(),
    });
    
    await waitFor(() => {
      expect(screen.getByText('⌘/Ctrl + ⌥ + F2')).toBeInTheDocument();
    });
    
    // Click save button
    const saveButton = screen.getByText('Save Shortcut');
    await user.click(saveButton);
    
    expect(mockOnSave).toHaveBeenCalledWith('CommandOrControl+Alt+F2');
  });

  it('calls onClose when cancel button is clicked', async () => {
    const user = userEvent.setup();
    render(<ShortcutDialog {...defaultProps} />);
    
    const cancelButton = screen.getByText('Cancel');
    await user.click(cancelButton);
    
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('disables save button when no shortcut is detected', () => {
    render(<ShortcutDialog {...defaultProps} />);
    
    const saveButton = screen.getByText('Save Shortcut');
    expect(saveButton).toBeDisabled();
  });

  it('disables save button while listening', async () => {
    render(<ShortcutDialog {...defaultProps} />);
    
    // Start listening
    const recordButton = screen.getByText('Click to Record Shortcut');
    fireEvent.click(recordButton);
    
    const saveButton = screen.getByText('Save Shortcut');
    expect(saveButton).toBeDisabled();
  });

  it('prevents dialog close while listening', () => {
    render(<ShortcutDialog {...defaultProps} />);
    
    // Start listening
    const recordButton = screen.getByText('Click to Record Shortcut');
    fireEvent.click(recordButton);
    
    // Try to close dialog with escape key
    fireEvent.keyDown(document, { key: 'Escape' });
    
    // Dialog should still be open and listening
    expect(screen.getByText('Press your key combination...')).toBeInTheDocument();
  });

  it('formats display shortcuts correctly', () => {
    render(<ShortcutDialog {...defaultProps} currentShortcut="CommandOrControl+Shift+Alt+F12" />);
    
    expect(screen.getByText('⌘/Ctrl + ⇧ + ⌥ + F12')).toBeInTheDocument();
  });

  it('handles arrow keys correctly', async () => {
    render(<ShortcutDialog {...defaultProps} />);
    
    // Start listening
    const recordButton = screen.getByText('Click to Record Shortcut');
    fireEvent.click(recordButton);
    
    // Test arrow key
    fireEvent.keyDown(document, {
      key: 'ArrowUp',
      ctrlKey: true,
      preventDefault: vi.fn(),
      stopPropagation: vi.fn(),
    });
    
    await waitFor(() => {
      expect(screen.getByText('⌘/Ctrl + ArrowUp')).toBeInTheDocument();
    });
  });

  it('handles function keys correctly', async () => {
    render(<ShortcutDialog {...defaultProps} />);
    
    // Start listening
    const recordButton = screen.getByText('Click to Record Shortcut');
    fireEvent.click(recordButton);
    
    // Test function key
    fireEvent.keyDown(document, {
      key: 'F10',
      ctrlKey: true,
      preventDefault: vi.fn(),
      stopPropagation: vi.fn(),
    });
    
    await waitFor(() => {
      expect(screen.getByText('⌘/Ctrl + F10')).toBeInTheDocument();
    });
  });

  it('shows helpful tips', () => {
    render(<ShortcutDialog {...defaultProps} />);
    
    expect(screen.getByText('Tips:')).toBeInTheDocument();
    expect(screen.getByText(/Use Ctrl\/Cmd \+ other keys for global shortcuts/)).toBeInTheDocument();
    expect(screen.getByText(/Avoid common system shortcuts/)).toBeInTheDocument();
    expect(screen.getByText(/Function keys \(F1-F12\) work well for shortcuts/)).toBeInTheDocument();
  });
});

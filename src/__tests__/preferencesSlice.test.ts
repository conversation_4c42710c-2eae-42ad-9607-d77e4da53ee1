import { configureStore } from '@reduxjs/toolkit';
import preferencesSlice, {
  setTheme,
  setAutoStart,
  setShowInSystemTray,
  setMaxHistoryLength,
  updateShortcut,
  resetShortcuts,
  loadPreferences,
  savePreferences,
  updateShortcuts,
} from '@/store/slices/preferencesSlice';
import type { PreferencesState, KeyboardShortcuts } from '@/types/clipboard';

const defaultShortcuts: KeyboardShortcuts = {
  toggleApp: 'CommandOrControl+Shift+V',
  clearHistory: 'CommandOrControl+Shift+Delete',
  toggleFavorite: 'CommandOrControl+D',
  copySelected: 'Enter',
  deleteSelected: 'Delete',
  search: 'CommandOrControl+F',
  navigateUp: 'ArrowUp',
  navigateDown: 'ArrowDown',
};

const createStore = (initialState: Partial<PreferencesState> = {}) => {
  return configureStore({
    reducer: {
      preferences: preferencesSlice,
    },
    preloadedState: {
      preferences: {
        theme: 'dark' as const,
        shortcuts: defaultShortcuts,
        autoStart: false,
        showInSystemTray: true,
        maxHistoryLength: 100,
        ...initialState,
      },
    },
  });
};

describe('preferencesSlice', () => {
  beforeEach(() => {
    globalThis.mockTauriInvoke.mockClear();
  });

  describe('reducers', () => {
    it('should handle setTheme', () => {
      const store = createStore();
      
      store.dispatch(setTheme('light'));
      
      expect(store.getState().preferences.theme).toBe('light');
    });

    it('should handle setAutoStart', () => {
      const store = createStore();
      
      store.dispatch(setAutoStart(true));
      
      expect(store.getState().preferences.autoStart).toBe(true);
    });

    it('should handle setShowInSystemTray', () => {
      const store = createStore();
      
      store.dispatch(setShowInSystemTray(false));
      
      expect(store.getState().preferences.showInSystemTray).toBe(false);
    });

    it('should handle setMaxHistoryLength', () => {
      const store = createStore();
      
      store.dispatch(setMaxHistoryLength(200));
      
      expect(store.getState().preferences.maxHistoryLength).toBe(200);
    });

    it('should handle updateShortcut', () => {
      const store = createStore();
      
      store.dispatch(updateShortcut({ key: 'copySelected', value: 'Space' }));
      
      expect(store.getState().preferences.shortcuts.copySelected).toBe('Space');
    });

    it('should handle resetShortcuts', () => {
      const store = createStore({
        shortcuts: {
          ...defaultShortcuts,
          copySelected: 'Space',
          navigateUp: 'k',
        },
      });
      
      store.dispatch(resetShortcuts());
      
      expect(store.getState().preferences.shortcuts).toEqual(defaultShortcuts);
    });
  });

  describe('async thunks', () => {
    describe('loadPreferences', () => {
      it('should load preferences successfully', async () => {
        const mockPreferences: PreferencesState = {
          theme: 'light',
          shortcuts: {
            ...defaultShortcuts,
            copySelected: 'Space',
          },
          autoStart: true,
          showInSystemTray: false,
          maxHistoryLength: 200,
        };
        globalThis.mockTauriInvoke.mockResolvedValueOnce(mockPreferences);
        const store = createStore();
        
        await store.dispatch(loadPreferences());
        
        const state = store.getState().preferences;
        expect(state).toEqual(mockPreferences);
        expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('get_preferences');
      });

      it('should handle load error', async () => {
        globalThis.mockTauriInvoke.mockRejectedValueOnce(new Error('Load failed'));
        const store = createStore();
        
        const result = await store.dispatch(loadPreferences());
        
        expect(result.type).toBe('preferences/load/rejected');
      });

      it('should merge loaded preferences with existing state', async () => {
        const partialPreferences = {
          theme: 'light' as const,
          autoStart: true,
        };
        globalThis.mockTauriInvoke.mockResolvedValueOnce(partialPreferences);
        const store = createStore({
          maxHistoryLength: 150,
        });
        
        await store.dispatch(loadPreferences());
        
        const state = store.getState().preferences;
        expect(state.theme).toBe('light');
        expect(state.autoStart).toBe(true);
        expect(state.maxHistoryLength).toBe(150); // Should keep existing value if not in loaded preferences
      });
    });

    describe('savePreferences', () => {
      it('should save preferences successfully', async () => {
        const preferencesToSave = {
          theme: 'light' as const,
          autoStart: true,
        };
        const savedPreferences: PreferencesState = {
          theme: 'light',
          shortcuts: defaultShortcuts,
          autoStart: true,
          showInSystemTray: true,
          maxHistoryLength: 100,
        };
        globalThis.mockTauriInvoke.mockResolvedValueOnce(savedPreferences);
        const store = createStore();
        
        await store.dispatch(savePreferences(preferencesToSave));
        
        const state = store.getState().preferences;
        expect(state).toEqual(savedPreferences);
        expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('save_preferences', {
          preferences: preferencesToSave,
        });
      });

      it('should handle save error', async () => {
        globalThis.mockTauriInvoke.mockRejectedValueOnce(new Error('Save failed'));
        const store = createStore();
        
        const result = await store.dispatch(savePreferences({ theme: 'light' }));
        
        expect(result.type).toBe('preferences/save/rejected');
      });
    });

    describe('updateShortcuts', () => {
      it('should update shortcuts successfully', async () => {
        const newShortcuts: KeyboardShortcuts = {
          ...defaultShortcuts,
          copySelected: 'Space',
          navigateUp: 'k',
          navigateDown: 'j',
        };
        globalThis.mockTauriInvoke.mockResolvedValueOnce(undefined);
        const store = createStore();
        
        await store.dispatch(updateShortcuts(newShortcuts));
        
        const state = store.getState().preferences;
        expect(state.shortcuts).toEqual(newShortcuts);
        expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('update_global_shortcuts', {
          shortcuts: newShortcuts,
        });
      });

      it('should handle update shortcuts error', async () => {
        globalThis.mockTauriInvoke.mockRejectedValueOnce(new Error('Update failed'));
        const store = createStore();
        
        const result = await store.dispatch(updateShortcuts(defaultShortcuts));
        
        expect(result.type).toBe('preferences/updateShortcuts/rejected');
      });
    });
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const store = configureStore({
        reducer: {
          preferences: preferencesSlice,
        },
      });
      const state = store.getState().preferences;
      
      expect(state).toEqual({
        theme: 'dark',
        shortcuts: defaultShortcuts,
        autoStart: false,
        showInSystemTray: true,
        maxHistoryLength: 100,
      });
    });
  });

  describe('edge cases', () => {
    it('should handle updating non-existent shortcut key', () => {
      const store = createStore();
      
      // TypeScript should prevent this, but testing runtime behavior
      store.dispatch(updateShortcut({ key: 'nonExistent' as any, value: 'Ctrl+X' }));
      
      const state = store.getState().preferences;
      expect((state.shortcuts as any).nonExistent).toBe('Ctrl+X');
    });

    it('should handle empty preferences object in savePreferences', async () => {
      const savedPreferences: PreferencesState = {
        theme: 'dark',
        shortcuts: defaultShortcuts,
        autoStart: false,
        showInSystemTray: true,
        maxHistoryLength: 100,
      };
      globalThis.mockTauriInvoke.mockResolvedValueOnce(savedPreferences);
      const store = createStore();
      
      await store.dispatch(savePreferences({}));
      
      expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('save_preferences', {
        preferences: {},
      });
    });

    it('should handle partial shortcuts update', () => {
      const store = createStore();
      
      store.dispatch(updateShortcut({ key: 'copySelected', value: 'Space' }));
      store.dispatch(updateShortcut({ key: 'navigateUp', value: 'k' }));
      
      const state = store.getState().preferences;
      expect(state.shortcuts.copySelected).toBe('Space');
      expect(state.shortcuts.navigateUp).toBe('k');
      expect(state.shortcuts.navigateDown).toBe('ArrowDown'); // Should remain unchanged
    });

    it('should handle theme toggle', () => {
      const store = createStore({ theme: 'dark' });
      
      store.dispatch(setTheme('light'));
      expect(store.getState().preferences.theme).toBe('light');
      
      store.dispatch(setTheme('dark'));
      expect(store.getState().preferences.theme).toBe('dark');
    });

    it('should handle boolean preference toggles', () => {
      const store = createStore({
        autoStart: false,
        showInSystemTray: true,
      });
      
      store.dispatch(setAutoStart(true));
      store.dispatch(setShowInSystemTray(false));
      
      const state = store.getState().preferences;
      expect(state.autoStart).toBe(true);
      expect(state.showInSystemTray).toBe(false);
    });

    it('should handle extreme maxHistoryLength values', () => {
      const store = createStore();
      
      store.dispatch(setMaxHistoryLength(0));
      expect(store.getState().preferences.maxHistoryLength).toBe(0);
      
      store.dispatch(setMaxHistoryLength(10000));
      expect(store.getState().preferences.maxHistoryLength).toBe(10000);
    });

    it('should preserve other shortcuts when resetting', () => {
      const customShortcuts = {
        ...defaultShortcuts,
        copySelected: 'Space',
        navigateUp: 'k',
        navigateDown: 'j',
      };
      const store = createStore({ shortcuts: customShortcuts });
      
      store.dispatch(resetShortcuts());
      
      const state = store.getState().preferences;
      expect(state.shortcuts).toEqual(defaultShortcuts);
      expect(state.shortcuts.copySelected).toBe('Enter'); // Should be reset to default
    });
  });

  describe('state consistency', () => {
    it('should maintain state consistency across multiple updates', () => {
      const store = createStore();
      
      store.dispatch(setTheme('light'));
      store.dispatch(setAutoStart(true));
      store.dispatch(updateShortcut({ key: 'copySelected', value: 'Space' }));
      store.dispatch(setMaxHistoryLength(200));
      
      const state = store.getState().preferences;
      expect(state.theme).toBe('light');
      expect(state.autoStart).toBe(true);
      expect(state.shortcuts.copySelected).toBe('Space');
      expect(state.maxHistoryLength).toBe(200);
      // Other values should remain unchanged
      expect(state.showInSystemTray).toBe(true);
    });

    it('should handle rapid successive updates', () => {
      const store = createStore();
      
      // Rapid theme changes
      store.dispatch(setTheme('light'));
      store.dispatch(setTheme('dark'));
      store.dispatch(setTheme('light'));
      
      expect(store.getState().preferences.theme).toBe('light');
      
      // Rapid shortcut updates
      store.dispatch(updateShortcut({ key: 'copySelected', value: 'Space' }));
      store.dispatch(updateShortcut({ key: 'copySelected', value: 'Enter' }));
      store.dispatch(updateShortcut({ key: 'copySelected', value: 'Return' }));
      
      expect(store.getState().preferences.shortcuts.copySelected).toBe('Return');
    });
  });

  describe('auto-start and system tray functionality', () => {
    it('should handle auto-start setting changes', () => {
      const store = createStore({ autoStart: false });
      
      store.dispatch(setAutoStart(true));
      expect(store.getState().preferences.autoStart).toBe(true);
      
      store.dispatch(setAutoStart(false));
      expect(store.getState().preferences.autoStart).toBe(false);
    });

    it('should handle system tray setting changes', () => {
      const store = createStore({ showInSystemTray: true });
      
      store.dispatch(setShowInSystemTray(false));
      expect(store.getState().preferences.showInSystemTray).toBe(false);
      
      store.dispatch(setShowInSystemTray(true));
      expect(store.getState().preferences.showInSystemTray).toBe(true);
    });

    it('should save auto-start and system tray preferences', async () => {
      const preferencesToSave = {
        autoStart: true,
        showInSystemTray: false,
      };
      const savedPreferences: PreferencesState = {
        theme: 'dark',
        shortcuts: defaultShortcuts,
        autoStart: true,
        showInSystemTray: false,
        maxHistoryLength: 100,
      };
      globalThis.mockTauriInvoke.mockResolvedValueOnce(savedPreferences);
      const store = createStore();
      
      await store.dispatch(savePreferences(preferencesToSave));
      
      const state = store.getState().preferences;
      expect(state.autoStart).toBe(true);
      expect(state.showInSystemTray).toBe(false);
      expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('save_preferences', {
        preferences: preferencesToSave,
      });
    });
  });
});

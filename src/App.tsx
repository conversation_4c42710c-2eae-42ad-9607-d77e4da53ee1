import React, { useEffect } from 'react';
import { ThemeProvider, CssBaseline } from '@mui/material';
import { Provider } from 'react-redux';
import { store, useAppDispatch } from '@/store';
import { loadPreferences } from '@/store/slices/preferencesSlice';
import { useClipboardMonitoring } from '@/hooks/useClipboardMonitoring';
import themeWithOverrides from '@/theme';
import AppLayout from '@/components/AppLayout';

const AppContent: React.FC = () => {
  const dispatch = useAppDispatch();

  // Initialize clipboard monitoring
  useClipboardMonitoring();

  useEffect(() => {
    // Initialize the app
    const initializeApp = async () => {
      try {
        // Load user preferences
        await dispatch(loadPreferences()).unwrap();
      } catch (error) {
        console.error('Failed to initialize app:', error);
      }
    };

    initializeApp();
  }, [dispatch]);

  return <AppLayout />;
};

function App() {
  return (
    <Provider store={store}>
      <ThemeProvider theme={themeWithOverrides}>
        <CssBaseline />
        <AppContent />
      </ThemeProvider>
    </Provider>
  );
}

export default App;

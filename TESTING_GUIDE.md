# Paste King Settings Testing Guide

## 🎯 Testing Overview

This guide will help you systematically test the auto-start and system tray functionality that was just implemented.

## 📋 Pre-Testing Checklist

- [x] App is running in development mode (`npm run tauri dev`)
- [x] No compilation errors
- [x] Console shows "System tray created successfully"
- [x] Console shows "Clipboard monitoring started successfully"

## 🧪 Test Plan

### **Test 1: System Tray Basic Functionality**

#### 1.1 Initial State Check
- [ ] **Look for system tray icon** in your system tray (top-right on macOS, bottom-right on Windows/Linux)
- [ ] **Expected**: Tray icon should be visible by default
- [ ] **If not visible**: Check console for errors

#### 1.2 Tray Icon Interactions
- [ ] **Left-click the tray icon**
  - Expected: Window should hide/show (toggle)
- [ ] **Double-click the tray icon**
  - Expected: Window should show and come to focus
- [ ] **Right-click the tray icon** (if supported on your OS)
  - Expected: Context menu with "Show/Hide" and "Quit" options

#### 1.3 Tray Menu Testing
- [ ] **Click "Show/Hide" from menu**
  - Expected: Window visibility should toggle
- [ ] **Click "Quit" from menu**
  - Expected: App should close completely

### **Test 2: System Tray Settings Toggle**

#### 2.1 Access Settings
- [ ] **Open the app window** (if hidden)
- [ ] **Navigate to Settings** (look for Settings tab/button)
- [ ] **Find "Show in System Tray" toggle**

#### 2.2 Toggle System Tray Off
- [ ] **Click the "Show in System Tray" toggle to OFF**
- [ ] **Check console output** - should see "System tray setting changed to: false"
- [ ] **Look at system tray area**
  - Expected: Tray icon should disappear
- [ ] **Try to close the app window**
  - Expected: App should close normally (not hide to tray)

#### 2.3 Toggle System Tray On
- [ ] **Click the "Show in System Tray" toggle to ON**
- [ ] **Check console output** - should see "System tray setting changed to: true"
- [ ] **Look at system tray area**
  - Expected: Tray icon should reappear
- [ ] **Try to close the app window**
  - Expected: Window should hide to tray (not close app)

### **Test 3: Auto-Start Functionality**

#### 3.1 Find Auto-Start Toggle
- [ ] **In Settings, find "Auto Start" toggle**
- [ ] **Note current state** (should be OFF by default)

#### 3.2 Enable Auto-Start
- [ ] **Click the "Auto Start" toggle to ON**
- [ ] **Check console output** - should see detailed auto-start logs
- [ ] **Look for success message** like "Auto-start enabled successfully"

#### 3.3 Verify Auto-Start Registration
**On macOS:**
- [ ] **Open System Preferences > Users & Groups > Login Items**
- [ ] **Look for "Paste King" in the list**

**On Windows:**
- [ ] **Open Task Manager > Startup tab**
- [ ] **Look for "Paste King" in the list**

**On Linux:**
- [ ] **Check autostart directory**: `ls ~/.config/autostart/`
- [ ] **Look for paste-king related files**

#### 3.4 Test Auto-Start (Optional - requires restart)
- [ ] **Restart your computer**
- [ ] **Check if Paste King launches automatically**
- [ ] **Verify it appears in system tray** (if tray is enabled)

#### 3.5 Disable Auto-Start
- [ ] **Click the "Auto Start" toggle to OFF**
- [ ] **Check console output** - should see "Auto-start disabled successfully"
- [ ] **Verify removal from system startup** (check same locations as 3.3)

### **Test 4: Settings Persistence**

#### 4.1 Test Settings Survival
- [ ] **Set both toggles to your preferred state**
- [ ] **Close the app completely** (Quit from tray menu or disable tray and close window)
- [ ] **Restart the app** (`npm run tauri dev`)
- [ ] **Check Settings page**
  - Expected: Both toggles should remember their previous state
- [ ] **Check system tray visibility**
  - Expected: Should match the saved setting

### **Test 5: Error Handling**

#### 5.1 Test Error Recovery
- [ ] **Toggle settings rapidly** (on/off/on/off quickly)
- [ ] **Check console for any errors**
- [ ] **Verify final state matches UI**

## 🐛 Troubleshooting

### Common Issues and Solutions

**System Tray Icon Not Visible:**
- Check console for "System tray created successfully"
- Try toggling the setting off and on
- Restart the app

**Auto-Start Not Working:**
- Check console for detailed error messages
- Verify app has necessary permissions
- On macOS: Check Security & Privacy settings

**Settings Not Persisting:**
- Check for file permission issues
- Look for error messages in console
- Verify app data directory is writable

## ✅ Success Criteria

All tests pass if:
- [x] System tray icon appears/disappears based on setting
- [x] Tray icon interactions work (click, menu)
- [x] Auto-start registers/unregisters with system
- [x] Settings persist across app restarts
- [x] No console errors during normal operation
- [x] UI toggles reflect actual backend state

## 📝 Test Results

**Date:** ___________
**Tester:** ___________
**OS:** ___________

### Results Summary:
- System Tray: ✅ / ❌
- Auto-Start: ✅ / ❌
- Settings Persistence: ✅ / ❌
- Error Handling: ✅ / ❌

### Notes:
_Add any observations, issues, or suggestions here_
